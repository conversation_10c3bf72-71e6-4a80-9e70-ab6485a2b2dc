// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyABh0Vc8CaJDaMn0fqjHAQz1_Bavafk9sM',
    appId: '1:57962231844:web:aba1f0f7af106e438a80b1',
    messagingSenderId: '57962231844',
    projectId: 'itower-v1',
    authDomain: 'itower-v1.firebaseapp.com',
    storageBucket: 'itower-v1.firebasestorage.app',
    measurementId: 'G-10CT3JHBTV',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyA75GRY3wPWkwSrAlcIzxM_XTFRseUbLdM',
    appId: '1:57962231844:android:ba9dc3a88e80a24f8a80b1',
    messagingSenderId: '57962231844',
    projectId: 'itower-v1',
    storageBucket: 'itower-v1.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyAjUMzVN1BTY4lzMIWoWRNenT_VVCJAx-k',
    appId: '1:57962231844:ios:9cf17b36d03e06978a80b1',
    messagingSenderId: '57962231844',
    projectId: 'itower-v1',
    storageBucket: 'itower-v1.firebasestorage.app',
    iosBundleId: 'com.example.admin',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyAjUMzVN1BTY4lzMIWoWRNenT_VVCJAx-k',
    appId: '1:57962231844:ios:9cf17b36d03e06978a80b1',
    messagingSenderId: '57962231844',
    projectId: 'itower-v1',
    storageBucket: 'itower-v1.firebasestorage.app',
    iosBundleId: 'com.example.admin',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyABh0Vc8CaJDaMn0fqjHAQz1_Bavafk9sM',
    appId: '1:57962231844:web:964eff2c02a3ceed8a80b1',
    messagingSenderId: '57962231844',
    projectId: 'itower-v1',
    authDomain: 'itower-v1.firebaseapp.com',
    storageBucket: 'itower-v1.firebasestorage.app',
    measurementId: 'G-WMKWNEDHY4',
  );
}
