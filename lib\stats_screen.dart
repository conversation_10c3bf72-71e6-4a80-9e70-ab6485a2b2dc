import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

class StatsScreen extends StatelessWidget {
  const StatsScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.indigo[100]!, Colors.white],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
      ),
      child: Center(
        child: StreamBuilder<QuerySnapshot>(
          stream: FirebaseFirestore.instance.collection('users').snapshots(),
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const CircularProgressIndicator();
            }
            final total = snapshot.hasData ? snapshot.data!.docs.length : 0;
            final activated = snapshot.hasData
                ? snapshot.data!.docs
                      .where(
                        (doc) =>
                            (doc.data()
                                as Map<String, dynamic>)['isActivated'] ==
                            true,
                      )
                      .length
                : 0;
            final notActivated = total - activated;
            return Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  'لوحة الإحصائيات',
                  style: TextStyle(
                    fontSize: 32,
                    fontWeight: FontWeight.bold,
                    color: Colors.indigo[900],
                    letterSpacing: 1.5,
                  ),
                ),
                SizedBox(height: 24),
                Wrap(
                  spacing: 24,
                  runSpacing: 24,
                  alignment: WrapAlignment.center,
                  children: [
                    _bigStatCard(
                      icon: Icons.people,
                      label: 'المستخدمين',
                      value: total,
                      color: Colors.blue,
                      gradient: LinearGradient(
                        colors: [Colors.blue[300]!, Colors.blue[700]!],
                      ),
                    ),
                    _bigStatCard(
                      icon: Icons.check_circle,
                      label: 'مفعلين',
                      value: activated,
                      color: Colors.green,
                      gradient: LinearGradient(
                        colors: [Colors.green[300]!, Colors.green[700]!],
                      ),
                    ),
                    _bigStatCard(
                      icon: Icons.cancel,
                      label: 'غير مفعلين',
                      value: notActivated,
                      color: Colors.red,
                      gradient: LinearGradient(
                        colors: [Colors.red[300]!, Colors.red[700]!],
                      ),
                    ),
                  ],
                ),
              ],
            );
          },
        ),
      ),
    );
  }
}

Widget _bigStatCard({
  required IconData icon,
  required String label,
  required int value,
  required Color color,
  required Gradient gradient,
}) {
  return Container(
    width: 180,
    height: 140,
    decoration: BoxDecoration(
      gradient: gradient,
      borderRadius: BorderRadius.circular(18),
      boxShadow: [
        BoxShadow(
          color: color.withOpacity(0.18),
          blurRadius: 12,
          offset: Offset(0, 4),
        ),
      ],
    ),
    child: Padding(
      padding: const EdgeInsets.all(18.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Container(
            width: 56,
            height: 56,
            decoration: BoxDecoration(
              color: Colors.white,
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: color.withOpacity(0.18),
                  blurRadius: 8,
                  offset: Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(icon, color: color, size: 22),
                SizedBox(height: 2),
                Text(
                  '$value',
                  style: TextStyle(
                    color: color,
                    fontWeight: FontWeight.bold,
                    fontSize: 18,
                  ),
                ),
              ],
            ),
          ),
          SizedBox(height: 16),
          Text(
            label,
            style: TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.w600,
              fontSize: 16,
              letterSpacing: 1.1,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    ),
  );
}
