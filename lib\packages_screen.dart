import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/services.dart';

class PackagesScreen extends StatefulWidget {
  const PackagesScreen({Key? key}) : super(key: key);

  @override
  State<PackagesScreen> createState() => _PackagesScreenState();
}

class _PackagesScreenState extends State<PackagesScreen> {
  // دالة لإظهار جميع الكودات وإدارتها (عرض + حذف)
  void _showManageCodesDialog() async {
    final scaffoldContext = context;
    showDialog(
      context: context,
      builder: (dialogContext) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Container(
            padding: const EdgeInsets.all(18),
            constraints: BoxConstraints(maxHeight: 500, minWidth: 350),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Row(
                  children: [
                    Icon(Icons.vpn_key, color: Colors.indigo),
                    SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'إدارة أكواد التفعيل',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 18,
                          color: Colors.indigo,
                        ),
                        textAlign: TextAlign.right,
                      ),
                    ),
                  ],
                ),
                Divider(height: 18),
                Expanded(
                  child: StreamBuilder<QuerySnapshot>(
                    stream: FirebaseFirestore.instance
                        .collection('activation_codes')
                        .orderBy('createdAt', descending: true)
                        .snapshots(),
                    builder: (streamContext, snapshot) {
                      if (snapshot.connectionState == ConnectionState.waiting) {
                        return Center(child: CircularProgressIndicator());
                      }
                      final codes = snapshot.data?.docs ?? [];
                      if (codes.isEmpty) {
                        return Center(
                          child: Text('لا يوجد أكواد تفعيل حالياً'),
                        );
                      }
                      return ListView.builder(
                        itemCount: codes.length,
                        itemBuilder: (itemContext, i) {
                          final data = codes[i].data() as Map<String, dynamic>;
                          final code = data['code'] ?? '-';
                          final packageType = data['packageType'] ?? '-';
                          final days = data['days'] ?? '-';
                          final used = data['used'] == true;
                          final createdAt = data['createdAt'] is Timestamp
                              ? (data['createdAt'] as Timestamp).toDate()
                              : null;
                          return Card(
                            margin: EdgeInsets.symmetric(vertical: 6),
                            child: ListTile(
                              leading: Icon(
                                used
                                    ? Icons.check_circle
                                    : Icons.radio_button_unchecked,
                                color: used ? Colors.green : Colors.grey,
                              ),
                              title: Text(
                                code,
                                textAlign: TextAlign.right,
                                style: TextStyle(fontWeight: FontWeight.bold),
                              ),
                              subtitle: Text(
                                'الباقة: $packageType | الأيام: $days${createdAt != null ? "\nتاريخ الإنشاء: ${createdAt.toLocal().toString().substring(0, 16)}" : ""}',
                                textAlign: TextAlign.right,
                              ),
                              trailing: IconButton(
                                icon: Icon(Icons.delete, color: Colors.red),
                                tooltip: 'حذف الكود',
                                onPressed: () async {
                                  final confirm = await showDialog<bool>(
                                    context: scaffoldContext,
                                    builder: (ctx) => AlertDialog(
                                      title: Text('تأكيد الحذف'),
                                      content: Text(
                                        'هل أنت متأكد أنك تريد حذف هذا الكود؟ لا يمكن التراجع عن العملية.',
                                      ),
                                      actions: [
                                        TextButton(
                                          child: Text('إلغاء'),
                                          onPressed: () =>
                                              Navigator.of(ctx).pop(false),
                                        ),
                                        ElevatedButton(
                                          child: Text(
                                            'حذف',
                                            style: TextStyle(
                                              color: Colors.white,
                                            ),
                                          ),
                                          style: ElevatedButton.styleFrom(
                                            backgroundColor: Colors.red,
                                          ),
                                          onPressed: () =>
                                              Navigator.of(ctx).pop(true),
                                        ),
                                      ],
                                    ),
                                  );
                                  if (confirm == true) {
                                    await FirebaseFirestore.instance
                                        .collection('activation_codes')
                                        .doc(code)
                                        .delete();
                                    ScaffoldMessenger.of(
                                      scaffoldContext,
                                    ).showSnackBar(
                                      SnackBar(
                                        content: Text('تم حذف الكود بنجاح'),
                                      ),
                                    );
                                  }
                                },
                              ),
                            ),
                          );
                        },
                      );
                    },
                  ),
                ),
                SizedBox(height: 8),
                Align(
                  alignment: Alignment.centerLeft,
                  child: TextButton(
                    child: Text(
                      'إغلاق',
                      style: TextStyle(color: Colors.indigo),
                    ),
                    onPressed: () => Navigator.of(dialogContext).pop(),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  // دالة توليد كود عشوائي بنمط خاص لكل باقة
  String _generateCodeForPackage(String packageType) {
    // توليد كود فريد بنمط خاص لكل باقة
    final now = DateTime.now().millisecondsSinceEpoch;
    String prefix;
    switch (packageType) {
      case 'شهري':
        prefix = 'M';
        break;
      case '3 أشهر':
        prefix = 'Q';
        break;
      case '6 أشهر':
        prefix = 'S';
        break;
      case 'سنوي':
        prefix = 'Y';
        break;
      default:
        prefix = 'X';
    }
    // إضافة جزء عشوائي وحصري
    final random = List.generate(
      5,
      (i) => String.fromCharCode(65 + ((now + i * 13) % 26)),
    ).join();
    final digits = (now % 100000).toString().padLeft(5, '0');
    return '$prefix-$random-$digits';
  }

  Future<int> _getUsersCount(String packageType) async {
    final usersSnapshot = await FirebaseFirestore.instance
        .collection('users')
        .where('activationType', isEqualTo: packageType)
        .get();
    return usersSnapshot.size;
  }

  void _showAddCodeDialog(String packageType, Color color) {
    final controller = TextEditingController();
    // مرر سياق الـ Scaffold الرئيسي من الـ build
    final scaffoldContext = context;
    showDialog(
      context: context,
      builder: (dialogContext) {
        return StatefulBuilder(
          builder: (context, setState) {
            return Dialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              child: Padding(
                padding: const EdgeInsets.all(20.0),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      'إضافة كود تفعيل للباقة',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 18,
                        color: color,
                      ),
                    ),
                    SizedBox(height: 18),
                    TextField(
                      controller: controller,
                      textAlign: TextAlign.end,
                      decoration: InputDecoration(
                        labelText: 'كود التفعيل',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        suffixIcon: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            IconButton(
                              icon: Icon(Icons.vpn_key, color: color),
                              tooltip: 'توليد كود عشوائي',
                              onPressed: () {
                                final code = _generateCodeForPackage(
                                  packageType,
                                );
                                setState(() {
                                  controller.text = code;
                                });
                              },
                            ),
                            if (controller.text.isNotEmpty)
                              IconButton(
                                icon: Icon(Icons.copy, color: color),
                                tooltip: 'نسخ الكود',
                                onPressed: () {
                                  Clipboard.setData(
                                    ClipboardData(text: controller.text),
                                  );
                                  // إظهار SnackBar في سياق الـ Scaffold الرئيسي
                                  Future.delayed(
                                    const Duration(milliseconds: 200),
                                    () {
                                      ScaffoldMessenger.of(
                                        scaffoldContext,
                                      ).showSnackBar(
                                        SnackBar(content: Text('تم نسخ الكود')),
                                      );
                                    },
                                  );
                                },
                              ),
                          ],
                        ),
                      ),
                    ),
                    SizedBox(height: 18),
                    Align(
                      alignment: Alignment.centerLeft,
                      child: ElevatedButton.icon(
                        icon: Icon(Icons.save, color: Colors.white),
                        label: Text('حفظ'),
                        style: ElevatedButton.styleFrom(backgroundColor: color),
                        onPressed: () async {
                          final code = controller.text.trim();
                          if (code.isNotEmpty) {
                            await _saveCodeToFirestore(
                              packageType,
                              code,
                            ); // الحفظ يتم فعلياً في قاعدة البيانات
                            Navigator.of(dialogContext).pop();
                            // إظهار SnackBar في سياق الـ Scaffold الرئيسي
                            Future.delayed(
                              const Duration(milliseconds: 200),
                              () {
                                ScaffoldMessenger.of(
                                  scaffoldContext,
                                ).showSnackBar(
                                  SnackBar(content: Text('تم حفظ الكود بنجاح')),
                                );
                              },
                            );
                          }
                        },
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }

  Future<void> _showUsersForPackage(String packageType, Color color) async {
    final usersSnapshot = await FirebaseFirestore.instance
        .collection('users')
        .where('activationType', isEqualTo: packageType)
        .get();
    final users = usersSnapshot.docs;
    showDialog(
      context: context,
      builder: (context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Container(
            padding: const EdgeInsets.all(18),
            constraints: BoxConstraints(maxHeight: 500, minWidth: 320),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Row(
                  children: [
                    Icon(Icons.group, color: color),
                    SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'مستخدمو باقة $packageType',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 18,
                          color: color,
                        ),
                        textAlign: TextAlign.right,
                      ),
                    ),
                  ],
                ),
                Divider(height: 18),
                Expanded(
                  child: users.isEmpty
                      ? Center(child: Text('لا يوجد مستخدمين لهذه الباقة'))
                      : ListView.builder(
                          itemCount: users.length,
                          itemBuilder: (context, i) {
                            final data = users[i].data();
                            final email = data['email'] ?? '-';
                            final code = data['activationCode'] ?? '-';
                            return ListTile(
                              leading: Icon(Icons.person, color: color),
                              title: Text(email, textAlign: TextAlign.right),
                              subtitle: Text(
                                'الكود: $code',
                                textAlign: TextAlign.right,
                              ),
                            );
                          },
                        ),
                ),
                SizedBox(height: 8),
                Align(
                  alignment: Alignment.centerLeft,
                  child: TextButton(
                    child: Text('إغلاق', style: TextStyle(color: color)),
                    onPressed: () => Navigator.of(context).pop(),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  final List<Map<String, dynamic>> packages = [
    {
      'name': 'شهري',
      'duration': '30 يوم',
      'icon': Icons.calendar_today,
      'color': Colors.blue,
      'activationCode': '',
    },
    {
      'name': '3 أشهر',
      'duration': '90 يوم',
      'icon': Icons.calendar_view_month,
      'color': Colors.green,
      'activationCode': '',
    },
    {
      'name': '6 أشهر',
      'duration': '180 يوم',
      'icon': Icons.date_range,
      'color': Colors.orange,
      'activationCode': '',
    },
    {
      'name': 'سنوي',
      'duration': '365 يوم',
      'icon': Icons.event,
      'color': Colors.purple,
      'activationCode': '',
    },
  ];

  Future<void> _setActivationCode(int index, String code) async {
    setState(() {
      packages[index]['activationCode'] = code;
    });
    final packageType = packages[index]['name'];
    await _saveCodeToFirestore(packageType, code);
  }

  Future<void> _saveCodeToFirestore(String packageType, String code) async {
    // سيتم حفظ الكود في مجموعة 'activation_codes' باسم المستند هو الكود نفسه
    final codesRef = FirebaseFirestore.instance.collection('activation_codes');
    final codeDoc = codesRef.doc(code);
    final existing = await codeDoc.get();
    if (existing.exists) {
      // الكود موجود مسبقاً، لا تحفظه مرة أخرى
      return;
    }
    int days = 0;
    switch (packageType) {
      case 'شهري':
        days = 30;
        break;
      case '3 أشهر':
        days = 90;
        break;
      case '6 أشهر':
        days = 180;
        break;
      case 'سنوي':
        days = 365;
        break;
      default:
        days = 0;
    }
    await codeDoc.set({
      'code': code,
      'packageType': packageType,
      'days': days,
      'used': false,
      'usedAt': null,
      'usedBy': null,
      'createdAt': FieldValue.serverTimestamp(),
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.indigo[50]!, Colors.white],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
        ),
      ),
      child: Column(
        children: [
          SizedBox(height: 16),
          Align(
            alignment: Alignment.centerLeft,
            child: ElevatedButton.icon(
              icon: Icon(Icons.vpn_key, color: Colors.white),
              label: Text('إدارة أكواد التفعيل'),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color.fromARGB(255, 164, 170, 184),
              ),
              onPressed: _showManageCodesDialog,
            ),
          ),
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(vertical: 32, horizontal: 16),
              itemCount: packages.length,
              itemBuilder: (context, i) {
                final pkg = packages[i];
                return FutureBuilder<int>(
                  future: _getUsersCount(pkg['name']),
                  builder: (context, snapshot) {
                    final usersCount = snapshot.data ?? 0;
                    return GestureDetector(
                      onTap: () =>
                          _showUsersForPackage(pkg['name'], pkg['color']),
                      child: Card(
                        elevation: 3,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(16),
                        ),
                        margin: const EdgeInsets.symmetric(vertical: 12),
                        child: Padding(
                          padding: const EdgeInsets.all(18.0),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              CircleAvatar(
                                backgroundColor: pkg['color'],
                                child: Icon(pkg['icon'], color: Colors.white),
                              ),
                              SizedBox(width: 12),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.end,
                                  children: [
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        Text(
                                          pkg['name'],
                                          style: TextStyle(
                                            fontSize: 20,
                                            fontWeight: FontWeight.bold,
                                            color: pkg['color'],
                                          ),
                                        ),
                                        Container(
                                          padding: EdgeInsets.symmetric(
                                            horizontal: 8,
                                            vertical: 2,
                                          ),
                                          decoration: BoxDecoration(
                                            color: pkg['color'].withOpacity(
                                              0.12,
                                            ),
                                            borderRadius: BorderRadius.circular(
                                              8,
                                            ),
                                          ),
                                          child: Text(
                                            'المستخدمين: $usersCount',
                                            style: TextStyle(
                                              fontSize: 13,
                                              color: pkg['color'],
                                              fontWeight: FontWeight.w500,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                    SizedBox(height: 4),
                                    Text(
                                      pkg['duration'],
                                      style: TextStyle(
                                        fontSize: 15,
                                        color: Colors.grey[700],
                                      ),
                                    ),
                                    SizedBox(height: 6),
                                    Align(
                                      alignment: Alignment.centerLeft,
                                      child: IconButton(
                                        icon: Icon(
                                          Icons.add_circle,
                                          color: pkg['color'],
                                          size: 28,
                                        ),
                                        tooltip: 'إضافة كود تفعيل',
                                        onPressed: () => _showAddCodeDialog(
                                          pkg['name'],
                                          pkg['color'],
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    );
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
