import 'package:flutter/material.dart';

Widget infoRow(IconData icon, String label, String value) {
  return Padding(
    padding: const EdgeInsets.symmetric(vertical: 2.5),
    child: Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        Text(
          value,
          style: TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
        ),
        SizedBox(width: 6),
        Icon(icon, size: 16, color: Colors.indigo[700]),
        SizedBox(width: 4),
        Text(
          label,
          style: TextStyle(
            fontSize: 13,
            color: Colors.indigo[900],
            fontWeight: FontWeight.w400,
          ),
        ),
      ],
    ),
  );
}
