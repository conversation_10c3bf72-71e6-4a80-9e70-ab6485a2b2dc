import 'package:flutter/material.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'info_row.dart';
import 'stats_screen.dart';
import 'packages_screen.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'firebase_options.dart';

// بطاقة إحصائية عصرية
class StatCardModern extends StatelessWidget {
  final String label;
  final int value;
  final Color color;
  final IconData icon;
  const StatCardModern({
    Key? key,
    required this.label,
    required this.value,
    required this.color,
    required this.icon,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 2, vertical: 4),
      padding: const EdgeInsets.symmetric(vertical: 6, horizontal: 8),
      decoration: BoxDecoration(
        color: color.withOpacity(0.07),
        borderRadius: BorderRadius.circular(10),
        boxShadow: [
          BoxShadow(
            color: color.withOpacity(0.08),
            blurRadius: 2,
            offset: Offset(0, 1),
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Container(
            width: 20,
            height: 20,
            decoration: BoxDecoration(color: color, shape: BoxShape.circle),
            child: Center(child: Icon(icon, color: Colors.white, size: 12)),
          ),
          SizedBox(width: 6),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                  color: color,
                  fontSize: 11,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              SizedBox(height: 0.5),
              Text(
                '$value',
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);
  runApp(const LoadingApp());
}

class LoadingApp extends StatelessWidget {
  const LoadingApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      home: FutureBuilder(
        future: Firebase.initializeApp(
          options: DefaultFirebaseOptions.currentPlatform,
        ),
        builder: (context, snapshot) {
          if (snapshot.connectionState != ConnectionState.done) {
            return const Scaffold(
              body: Center(child: CircularProgressIndicator()),
            );
          }
          return const AdminPanelApp();
        },
      ),
      debugShowCheckedModeBanner: false,
    );
  }
}

class AdminPanelApp extends StatelessWidget {
  const AdminPanelApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'لوحة تحكم الإدارة',
      theme: ThemeData(primarySwatch: Colors.blue),
      home: const AdminLoginScreen(),
      debugShowCheckedModeBanner: false,
    );
  }
}

class AdminLoginScreen extends StatefulWidget {
  const AdminLoginScreen({Key? key}) : super(key: key);

  @override
  State<AdminLoginScreen> createState() => _AdminLoginScreenState();
}

class _AdminLoginScreenState extends State<AdminLoginScreen> {
  User? user;
  final List<String> allowedAdmins = [
    '<EMAIL>', // أنت المدير الوحيد
  ];
  String? error;

  @override
  void initState() {
    super.initState();
    // تحقق من حالة تسجيل الدخول عند بدء الشاشة
    final currentUser = FirebaseAuth.instance.currentUser;
    if (currentUser != null && allowedAdmins.contains(currentUser.email)) {
      user = currentUser;
    }
  }

  Future<void> signInWithGoogle() async {
    try {
      final GoogleSignInAccount? googleUser = await GoogleSignIn().signIn();
      if (googleUser == null) return;
      final GoogleSignInAuthentication googleAuth =
          await googleUser.authentication;
      final credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );
      final userCredential = await FirebaseAuth.instance.signInWithCredential(
        credential,
      );
      if (!allowedAdmins.contains(userCredential.user?.email)) {
        if (mounted) {
          setState(() {
            error = 'غير مصرح لك بالدخول إلى لوحة التحكم.';
          });
        }
        await FirebaseAuth.instance.signOut();
        await GoogleSignIn().signOut();
        return;
      }
      if (mounted) {
        setState(() {
          user = userCredential.user;
          error = null;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          error = 'حدث خطأ أثناء تسجيل الدخول: $e';
        });
      }
      print('Google Sign-In Error: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    if (user == null) {
      return Scaffold(
        body: Center(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text('تسجيل دخول المدير', style: TextStyle(fontSize: 24)),
              const SizedBox(height: 20),
              ElevatedButton.icon(
                icon: const Icon(Icons.login),
                label: const Text('تسجيل دخول Google'),
                onPressed: signInWithGoogle,
              ),
              if (error != null) ...[
                const SizedBox(height: 20),
                Text(error!, style: const TextStyle(color: Colors.red)),
              ],
            ],
          ),
        ),
      );
    }
    // بعد الدخول الناجح، انتقل لجدول المستخدمين (سيتم إضافته لاحقاً)
    // بعد الدخول الناجح، انتقل لجدول المستخدمين مع الإحصائيات والبحث والتنبيهات
    return AdminDashboard(
      onLogout: () async {
        await FirebaseAuth.instance.signOut();
        await GoogleSignIn().signOut();
        setState(() {
          user = null;
        });
      },
    );
  }
}

// لوحة تحكم متكاملة مع بحث وإحصائيات وتنبيهات
class AdminDashboard extends StatefulWidget {
  final VoidCallback onLogout;
  const AdminDashboard({Key? key, required this.onLogout}) : super(key: key);

  @override
  State<AdminDashboard> createState() => _AdminDashboardState();
}

class _AdminDashboardState extends State<AdminDashboard> {
  int selectedIndex = 0;
  String search = '';
  String? snackbarMsg;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: selectedIndex,
        onTap: (index) => setState(() => selectedIndex = index),
        selectedItemColor: Colors.blue[800],
        unselectedItemColor: Colors.grey[600],
        showSelectedLabels: true,
        showUnselectedLabels: false,
        type: BottomNavigationBarType.fixed,
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.dashboard_rounded),
            label: 'الإحصائيات',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.people_alt_rounded),
            label: 'المستخدمين',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.card_membership),
            label: 'البطاقات',
          ),
        ],
      ),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        title: Text(
          'لوحة التحكم',
          style: TextStyle(
            color: Colors.blue[900],
            fontWeight: FontWeight.bold,
            fontSize: 20,
          ),
        ),
        leading: IconButton(
          icon: Icon(Icons.menu, color: Colors.blue[900]),
          onPressed: () {},
        ),
        actions: [
          IconButton(
            icon: Icon(Icons.notifications_active, color: Colors.blue[900]),
            onPressed: () {},
          ),
          IconButton(
            icon: Icon(Icons.logout, color: Colors.blue[900]),
            tooltip: 'تسجيل خروج',
            onPressed: widget.onLogout,
          ),
        ],
      ),
      body: Container(color: Colors.grey[100], child: _buildBody()),
    );
  }

  Widget _buildBody() {
    if (selectedIndex == 0) {
      // شاشة الإحصائيات المنفصلة
      return const StatsScreen();
    } else if (selectedIndex == 1) {
      // شاشة المستخدمين (تصميم بطاقات قائمة)
      return Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [Colors.white, Colors.indigo[50]!],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
        ),
        child: Column(
          children: [
            Padding(
              padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
              child: TextField(
                decoration: InputDecoration(
                  labelText: 'بحث عن مستخدم',
                  prefixIcon: Icon(Icons.search, color: Colors.indigo),
                  filled: true,
                  fillColor: Colors.white,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  contentPadding: EdgeInsets.symmetric(
                    vertical: 0,
                    horizontal: 16,
                  ),
                ),
                style: TextStyle(fontSize: 15),
                onChanged: (val) => setState(() => search = val.trim()),
              ),
            ),
            Expanded(
              child: StreamBuilder<QuerySnapshot>(
                stream: FirebaseFirestore.instance
                    .collection('users')
                    .snapshots(),
                builder: (context, snapshot) {
                  if (snapshot.connectionState == ConnectionState.waiting) {
                    return const Center(child: CircularProgressIndicator());
                  }
                  if (!snapshot.hasData || snapshot.data!.docs.isEmpty) {
                    return const Center(
                      child: Text(
                        'لا يوجد مستخدمين بعد.',
                        style: TextStyle(fontSize: 18),
                      ),
                    );
                  }
                  final users = snapshot.data!.docs.where((doc) {
                    final data = doc.data() as Map<String, dynamic>;
                    final email = data['email'] ?? '';
                    return search.isEmpty || email.contains(search);
                  }).toList();

                  // إحصائيات عصرية

                  return Column(
                    children: [
                      Expanded(
                        child: ListView.separated(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          itemCount: users.length,
                          separatorBuilder: (_, __) => SizedBox(height: 8),
                          itemBuilder: (context, i) {
                            final doc = users[i];
                            final data = doc.data() as Map<String, dynamic>;
                            final email = data['email'] ?? '-';
                            final userId = data['deviceId']?.toString() ?? '-';
                            final packageType =
                                data['activationType']?.toString() ?? '-';
                            final activationCode =
                                data['activationCode'] ?? '-';
                            final activationAmount =
                                data['activationAmount']?.toString() ?? '-';
                            final activationPaid =
                                data['activationPaid'] == true
                                ? 'مدفوع'
                                : 'غير مدفوع';
                            final expiryDate = data['expiryDate'] != null
                                ? (data['expiryDate'] as Timestamp)
                                      .toDate()
                                      .toString()
                                      .split(' ')[0]
                                : '-';
                            final trialActive = data['trialActive'] == true
                                ? 'تجربة مفعلة'
                                : 'بدون تجربة';
                            final trialStartDate = data['trialStart'] != null
                                ? (data['trialStart'] as Timestamp).toDate()
                                : null;
                            final trialStart = trialStartDate != null
                                ? trialStartDate.toString().split(' ')[0]
                                : '-';
                            final isActivated = data['isActivated'] == true;
                            int daysLeft = 0;
                            if (data['trialActive'] == true &&
                                data['expiryDate'] != null) {
                              final now = DateTime.now();
                              final end = (data['expiryDate'] as Timestamp)
                                  .toDate();
                              daysLeft = end.difference(now).inDays;
                              daysLeft = daysLeft > 0 ? daysLeft : 0;
                            }
                            return Card(
                              elevation: 2,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Padding(
                                padding: const EdgeInsets.symmetric(
                                  vertical: 8,
                                  horizontal: 12,
                                ),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.end,
                                  children: [
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        ElevatedButton(
                                          style: ElevatedButton.styleFrom(
                                            backgroundColor: isActivated
                                                ? Colors.red
                                                : Colors.green,
                                            shape: RoundedRectangleBorder(
                                              borderRadius:
                                                  BorderRadius.circular(8),
                                            ),
                                            padding: EdgeInsets.symmetric(
                                              horizontal: 10,
                                              vertical: 6,
                                            ),
                                          ),
                                          child: Text(
                                            isActivated ? 'إيقاف' : 'تفعيل',
                                            style: TextStyle(fontSize: 13),
                                          ),
                                          onPressed: () async {
                                            await doc.reference.update({
                                              'isActivated': !isActivated,
                                            });
                                            setState(() {
                                              snackbarMsg = isActivated
                                                  ? 'تم إيقاف المستخدم'
                                                  : 'تم تفعيل المستخدم';
                                            });
                                            ScaffoldMessenger.of(
                                              context,
                                            ).showSnackBar(
                                              SnackBar(
                                                content: Text(snackbarMsg!),
                                              ),
                                            );
                                          },
                                        ),
                                        Row(
                                          children: [
                                            CircleAvatar(
                                              backgroundColor: isActivated
                                                  ? Colors.green
                                                  : Colors.red,
                                              radius: 15,
                                              child: Icon(
                                                isActivated
                                                    ? Icons.check
                                                    : Icons.close,
                                                color: Colors.white,
                                                size: 16,
                                              ),
                                            ),
                                            SizedBox(width: 8),
                                            Text(
                                              email,
                                              style: TextStyle(
                                                fontWeight: FontWeight.bold,
                                                fontSize: 15,
                                              ),
                                            ),
                                            SizedBox(width: 8),
                                            IconButton(
                                              icon: Icon(
                                                Icons.delete,
                                                color: Colors.red,
                                              ),
                                              tooltip: 'حذف المستخدم',
                                              onPressed: () async {
                                                final confirm = await showDialog<bool>(
                                                  context: context,
                                                  builder: (ctx) => AlertDialog(
                                                    title: Text('تأكيد الحذف'),
                                                    content: Text(
                                                      'هل أنت متأكد أنك تريد حذف هذا المستخدم؟ لا يمكن التراجع عن العملية.',
                                                    ),
                                                    actions: [
                                                      TextButton(
                                                        child: Text('إلغاء'),
                                                        onPressed: () =>
                                                            Navigator.of(
                                                              ctx,
                                                            ).pop(false),
                                                      ),
                                                      ElevatedButton(
                                                        child: Text(
                                                          'حذف',
                                                          style: TextStyle(
                                                            color: Colors.white,
                                                          ),
                                                        ),
                                                        style:
                                                            ElevatedButton.styleFrom(
                                                              backgroundColor:
                                                                  Colors.red,
                                                            ),
                                                        onPressed: () =>
                                                            Navigator.of(
                                                              ctx,
                                                            ).pop(true),
                                                      ),
                                                    ],
                                                  ),
                                                );
                                                if (confirm == true) {
                                                  await doc.reference.delete();
                                                  ScaffoldMessenger.of(
                                                    context,
                                                  ).showSnackBar(
                                                    SnackBar(
                                                      content: Text(
                                                        'تم حذف المستخدم بنجاح',
                                                      ),
                                                    ),
                                                  );
                                                }
                                              },
                                            ),
                                          ],
                                        ),
                                      ],
                                    ),
                                    Divider(height: 18, thickness: 1),
                                    Align(
                                      alignment: Alignment.centerRight,
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.end,
                                        children: [
                                          infoRow(
                                            Icons.perm_device_information,
                                            'معرف الهاتف',
                                            userId,
                                          ),
                                          infoRow(
                                            Icons.card_membership,
                                            'نوع الباقة',
                                            packageType,
                                          ),
                                          infoRow(
                                            Icons.vpn_key,
                                            'كود التفعيل',
                                            activationCode,
                                          ),
                                          infoRow(
                                            Icons.attach_money,
                                            'مبلغ التفعيل',
                                            activationAmount,
                                          ),
                                          infoRow(
                                            Icons.payment,
                                            'حالة الدفع',
                                            activationPaid,
                                          ),
                                          infoRow(
                                            Icons.date_range,
                                            'تاريخ البداية',
                                            trialStart,
                                          ),
                                          infoRow(
                                            Icons.event,
                                            'تاريخ الانتهاء',
                                            expiryDate,
                                          ),
                                          infoRow(
                                            Icons.hourglass_bottom,
                                            'حالة التجربة',
                                            trialActive,
                                          ),
                                          infoRow(
                                            Icons.timer,
                                            'الأيام المتبقية',
                                            expiryDate == '-'
                                                ? '-'
                                                : '$daysLeft يوم',
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                    ],
                  );
                },
              ),
            ),
          ],
        ),
      );
    } else if (selectedIndex == 2) {
      // شاشة الباقات
      return const PackagesScreen();
    }
    return Container();
  }
}

class StatCard extends StatelessWidget {
  final String label;
  final int value;
  final Color color;
  const StatCard({
    Key? key,
    required this.label,
    required this.value,
    required this.color,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      color: color.withOpacity(0.1),
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          children: [
            Text(
              label,
              style: TextStyle(fontWeight: FontWeight.bold, color: color),
            ),
            const SizedBox(height: 4),
            Text('$value', style: TextStyle(fontSize: 18, color: color)),
          ],
        ),
      ),
    );
  }
}
